<template>
  <view class="chat-container">
    <!-- Header -->
    <view class="chat-header">
      <view class="header-left">
        <view class="back-btn" @click="goBack">
          <text class="back-icon">‹</text>
        </view>
        <text class="contact-count">{{ contactCount }}</text>
      </view>
      <view class="header-center">
        <text class="contact-name">{{ you.nickname || '乖乖' }}</text>
      </view>
      <view class="header-right">
        <text class="more-icon">⋯</text>
      </view>
    </view>

    <!-- Messages -->
    <scroll-view class="chat-messages" scroll-y :scroll-top="scrollTop">
      <view v-for="(message, index) in messages" :key="index">
        <!-- Timestamp -->
        <view v-if="shouldShowTimestamp(message, index)" class="timestamp">
          {{ formatTimestamp(message.timestamp) }}
        </view>
        <!-- Message -->
        <view class="message-wrapper" :class="{ 'my-message': message.sender === 'me' }">
          <view class="message-row">
            <image v-if="message.sender !== 'me'" class="avatar" :src="you.avatar || defaultAvatar" />
            <view class="message-content" :class="{ 'my-content': message.sender === 'me' }">
              <text>{{ message.text }}</text>
            </view>
            <image v-if="message.sender === 'me'" class="avatar my-avatar" :src="me.avatar || defaultAvatar" />
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- Input -->
    <view class="chat-input">
      <view class="input-container">
        <picker :range="senders" @change="switchSender" class="sender-picker">
          <view class="sender-switch">
            <text>{{ currentSender === 'me' ? '我' : '对方' }}</text>
          </view>
        </picker>
        <view class="voice-icon" @click="switchMode('voice')">
          <text>🎤</text>
        </view>
        <input v-if="inputMode === 'text'" v-model="newMessage" class="input-field" placeholder="输入消息..." />
        <view v-else class="voice-button">按住 说话</view>
        <view class="emoji-icon" @click="openEmojiPicker">
          <text>😊</text>
        </view>
        <view class="plus-icon" @click="openFunctionPanel">
          <text>+</text>
        </view>
      </view>
      <view class="home-indicator"></view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import { useChatStore } from '../../stores/chat';
import { storeToRefs } from 'pinia';

const chatStore = useChatStore();
const { me, you } = storeToRefs(chatStore);

const messages = ref([]);
const newMessage = ref('');
const currentSender = ref('me');
const senders = ['我', '对方'];
const inputMode = ref('text'); // 'text' or 'voice'
const scrollTop = ref(0);
const contactCount = ref('41');
const defaultAvatar = '../../static/logo.png';

onMounted(() => {
  // 隐藏默认导航栏，使用自定义头部
  uni.hideNavigationBarLoading();

  // 添加一些示例消息
  const now = new Date();
  const sampleMessages = [
    {
      type: 'text',
      text: 'OK',
      sender: 'me',
      timestamp: new Date(now.getTime() - 3600000) // 1小时前
    },
    {
      type: 'text',
      text: '下没',
      sender: 'you',
      timestamp: new Date(now.getTime() - 3000000) // 50分钟前
    },
    {
      type: 'text',
      text: '刚下',
      sender: 'me',
      timestamp: new Date(now.getTime() - 2400000) // 40分钟前
    },
    {
      type: 'text',
      text: '蔡家要到了',
      sender: 'me',
      timestamp: new Date(now.getTime() - 1800000) // 30分钟前
    },
    {
      type: 'text',
      text: '来了',
      sender: 'you',
      timestamp: new Date(now.getTime() - 1200000) // 20分钟前
    },
    {
      type: 'text',
      text: '下班了',
      sender: 'me',
      timestamp: new Date(now.getTime() - 600000) // 10分钟前
    },
    {
      type: 'text',
      text: '蔡家到了',
      sender: 'me',
      timestamp: new Date(now.getTime() - 300000) // 5分钟前
    },
    {
      type: 'text',
      text: '出发没 没出发我做公交车',
      sender: 'me',
      timestamp: new Date(now.getTime() - 60000) // 1分钟前
    }
  ];

  messages.value = sampleMessages;
});

const goBack = () => {
  uni.navigateBack();
};

const switchSender = (e) => {
  currentSender.value = e.detail.value === 0 ? 'me' : 'you';
};

const sendMessage = () => {
  if (newMessage.value.trim() === '') return;
  addMessage({
    type: 'text',
    text: newMessage.value,
    sender: currentSender.value,
  });
  newMessage.value = '';
};

const addMessage = (message) => {
  const messageWithTimestamp = {
    ...message,
    timestamp: new Date(),
  };
  messages.value.push(messageWithTimestamp);
  nextTick(() => {
    scrollTop.value = 999999; // Scroll to bottom
  });
};

const shouldShowTimestamp = (message, index) => {
  if (index === 0) return true; // Always show for the first message
  const prevMessage = messages.value[index - 1];
  const timeDiff = message.timestamp.getTime() - prevMessage.timestamp.getTime();
  return timeDiff > 5 * 60 * 1000; // Show if more than 5 minutes apart
};

const formatTimestamp = (date) => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const timeStr = `${hours}:${minutes}`;

  // 判断是否是今天
  if (messageDate.getTime() === today.getTime()) {
    return timeStr;
  }

  // 判断是否是昨天
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  if (messageDate.getTime() === yesterday.getTime()) {
    return `昨天 ${timeStr}`;
  }

  // 判断是否是本周
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  const daysDiff = Math.floor((today.getTime() - messageDate.getTime()) / (24 * 60 * 60 * 1000));
  if (daysDiff < 7) {
    return `${weekdays[date.getDay()]} ${timeStr}`;
  }

  // 其他情况显示具体日期
  const month = (date.getMonth() + 1).toString();
  const day = date.getDate().toString();
  return `${month}月${day}日 ${timeStr}`;
};

const switchMode = (mode) => {
  inputMode.value = mode;
};

const openEmojiPicker = () => {
  console.log('Open emoji picker');
};

const openFunctionPanel = () => {
  uni.showActionSheet({
    itemList: ['红包'],
    success: (res) => {
      if (res.tapIndex === 0) {
        createRedPacket();
      }
    }
  });
};

const createRedPacket = () => {
  uni.showModal({
    title: '创建红包',
    content: '请输入红包金额',
    editable: true,
    success: (res) => {
      if (res.confirm) {
        const amount = parseFloat(res.content);
        if (isNaN(amount) || amount <= 0) {
          uni.showToast({
            title: '请输入有效的金额',
            icon: 'none'
          });
          return;
        }
        uni.showModal({
          title: '创建红包',
          content: '请输入祝福语',
          editable: true,
          success: (res2) => {
            if (res2.confirm) {
              const blessing = res2.content || '恭喜发财，大吉大利';
              addMessage({
                type: 'red-packet',
                sender: currentSender.value,
                amount: amount.toFixed(2),
                blessing: blessing,
              });
            }
          }
        });
      }
    }
  });
};

const openRedPacket = (message) => {
  // Placeholder for opening red packet details
  uni.showToast({
    title: `打开了来自 ${message.sender === 'me' ? me.value.nickname : you.value.nickname} 的红包`,
    icon: 'none'
  });
};

</script>

<style scoped>
/* 整体容器 */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #ededed;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

/* 顶部导航栏 */
.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
  background-color: #f7f7f7;
  border-bottom: 0.5px solid #d9d9d9;
  position: relative;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.back-btn {
  margin-right: 8px;
}

.back-icon {
  font-size: 24px;
  color: #007aff;
  font-weight: 300;
}

.contact-count {
  font-size: 17px;
  color: #007aff;
  font-weight: 400;
}

.header-center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.contact-name {
  font-size: 17px;
  color: #000;
  font-weight: 600;
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.more-icon {
  font-size: 20px;
  color: #007aff;
  font-weight: bold;
}

/* 消息区域 */
.chat-messages {
  flex: 1;
  padding: 0;
  background-color: #ededed;
}

.timestamp {
  text-align: center;
  color: #b2b2b2;
  font-size: 13px;
  margin: 8px 0;
  line-height: 1.2;
}

/* 消息容器 */
.message-wrapper {
  margin-bottom: 12px;
  padding: 0 12px;
}

.message-row {
  display: flex;
  align-items: flex-end;
}

.my-message .message-row {
  flex-direction: row-reverse;
}

/* 头像 */
.avatar {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  flex-shrink: 0;
}

.my-avatar {
  margin-left: 8px;
}

.avatar:not(.my-avatar) {
  margin-right: 8px;
}

/* 消息气泡 */
.message-content {
  max-width: 60%;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 16px;
  line-height: 1.4;
  word-wrap: break-word;
  position: relative;
}

/* 对方消息气泡 */
.message-content:not(.my-content) {
  background-color: #ffffff;
  color: #000;
}

/* 我的消息气泡 */
.my-content {
  background-color: #95ec69;
  color: #000;
}

/* 底部输入区域 */
.chat-input {
  background-color: #f7f7f7;
  border-top: 0.5px solid #d9d9d9;
  padding-bottom: env(safe-area-inset-bottom);
}

.input-container {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  min-height: 50px;
}

.voice-icon, .emoji-icon, .plus-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin: 0 4px;
}

.input-field {
  flex: 1;
  height: 36px;
  padding: 0 12px;
  border-radius: 6px;
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  font-size: 16px;
  margin: 0 8px;
}

.voice-button {
  flex: 1;
  height: 36px;
  line-height: 36px;
  text-align: center;
  border-radius: 6px;
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  font-size: 16px;
  margin: 0 8px;
}

/* 发送方切换器 */
.sender-picker {
  position: fixed;
  left: 12px;
  bottom: 80px;
  z-index: 100;
}

.sender-switch {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #ffffff;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 底部安全区域指示器 */
.home-indicator {
  height: 4px;
  width: 134px;
  background-color: #000;
  border-radius: 2px;
  margin: 8px auto 4px;
  opacity: 0.3;
}

/* 适配不同屏幕尺寸 */
@media (max-width: 375px) {
  .message-content {
    max-width: 65%;
    font-size: 15px;
  }

  .avatar {
    width: 36px;
    height: 36px;
  }
}

@media (min-width: 414px) {
  .message-content {
    max-width: 55%;
  }
}
</style>