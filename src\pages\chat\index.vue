<template>
  <view class="chat-container">
    <!-- Header -->
    <view class="chat-header">
      <text>{{ you.nickname }}</text>
    </view>

    <!-- Messages -->
    <scroll-view class="chat-messages" scroll-y :scroll-top="scrollTop">
      <view v-for="(message, index) in messages" :key="index">
        <!-- Timestamp -->
        <view v-if="shouldShowTimestamp(message, index)" class="timestamp">
          {{ formatTimestamp(message.timestamp) }}
        </view>
        <!-- Message -->
        <view class="message" :class="{ 'my-message': message.sender === 'me' }">
          <image class="avatar" :src="message.sender === 'me' ? me.avatar : you.avatar" />
          <!-- Text Message -->
          <view v-if="message.type === 'text'" class="message-content">
            <text>{{ message.text }}</text>
          </view>
          <!-- Red Packet -->
          <view v-if="message.type === 'red-packet'" class="red-packet-content" @click="openRedPacket(message)">
            <view class="red-packet-icon">🧧</view>
            <view class="red-packet-text">
              <text>{{ message.blessing }}</text>
              <text class="red-packet-subtext">微信红包</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- Input -->
    <view class="chat-input">
      <picker :range="senders" @change="switchSender" class="sender-picker">
        <view class="sender-switch">
          <text>{{ currentSender === 'me' ? '我' : '对方' }}</text>
        </view>
      </picker>
      <image class="icon" src="../../static/voice.svg" @click="switchMode('voice')" />
      <input v-if="inputMode === 'text'" v-model="newMessage" class="input-field" placeholder="输入消息..." />
      <view v-else class="voice-button">按住 说话</view>
      <image class="icon" src="../../static/emoji.svg" @click="openEmojiPicker" />
      <image class="icon" src="../../static/plus.svg" @click="openFunctionPanel" />
      <button v-if="newMessage" @click="sendMessage" class="send-button">发送</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import { useChatStore } from '../../stores/chat';
import { storeToRefs } from 'pinia';

const chatStore = useChatStore();
const { me, you } = storeToRefs(chatStore);

const messages = ref([]);
const newMessage = ref('');
const currentSender = ref('me');
const senders = ['我', '对方'];
const inputMode = ref('text'); // 'text' or 'voice'
const scrollTop = ref(0);

onMounted(() => {
  uni.setNavigationBarTitle({
    title: you.value.nickname
  });
});

const switchSender = (e) => {
  currentSender.value = e.detail.value === 0 ? 'me' : 'you';
};

const sendMessage = () => {
  if (newMessage.value.trim() === '') return;
  addMessage({
    type: 'text',
    text: newMessage.value,
    sender: currentSender.value,
  });
  newMessage.value = '';
};

const addMessage = (message) => {
  const messageWithTimestamp = {
    ...message,
    timestamp: new Date(),
  };
  messages.value.push(messageWithTimestamp);
  nextTick(() => {
    scrollTop.value = 999999; // Scroll to bottom
  });
};

const shouldShowTimestamp = (message, index) => {
  if (index === 0) return true; // Always show for the first message
  const prevMessage = messages.value[index - 1];
  const timeDiff = message.timestamp.getTime() - prevMessage.timestamp.getTime();
  return timeDiff > 5 * 60 * 1000; // Show if more than 5 minutes apart
};

const formatTimestamp = (date) => {
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
};

const switchMode = (mode) => {
  inputMode.value = mode;
};

const openEmojiPicker = () => {
  console.log('Open emoji picker');
};

const openFunctionPanel = () => {
  uni.showActionSheet({
    itemList: ['红包'],
    success: (res) => {
      if (res.tapIndex === 0) {
        createRedPacket();
      }
    }
  });
};

const createRedPacket = () => {
  uni.showModal({
    title: '创建红包',
    content: '请输入红包金额',
    editable: true,
    success: (res) => {
      if (res.confirm) {
        const amount = parseFloat(res.content);
        if (isNaN(amount) || amount <= 0) {
          uni.showToast({
            title: '请输入有效的金额',
            icon: 'none'
          });
          return;
        }
        uni.showModal({
          title: '创建红包',
          content: '请输入祝福语',
          editable: true,
          success: (res2) => {
            if (res2.confirm) {
              const blessing = res2.content || '恭喜发财，大吉大利';
              addMessage({
                type: 'red-packet',
                sender: currentSender.value,
                amount: amount.toFixed(2),
                blessing: blessing,
              });
            }
          }
        });
      }
    }
  });
};

const openRedPacket = (message) => {
  // Placeholder for opening red packet details
  uni.showToast({
    title: `打开了来自 ${message.sender === 'me' ? me.value.nickname : you.value.nickname} 的红包`,
    icon: 'none'
  });
};

</script>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}
.chat-header {
  padding: 10px;
  text-align: center;
  border-bottom: 1px solid #eee;
  background-color: #f8f8f8;
}
.chat-messages {
  flex: 1;
  padding: 10px;
  overflow-y: auto;
}
.timestamp {
  text-align: center;
  color: #999;
  font-size: 12px;
  margin: 10px 0;
}
.message {
  display: flex;
  margin-bottom: 15px;
}
.my-message {
  flex-direction: row-reverse;
}
.avatar {
  width: 40px;
  height: 40px;
  border-radius: 5px;
  object-fit: cover;
  flex-shrink: 0;
}
.message-content {
  position: relative;
  background-color: #ffffff;
  padding: 10px 12px;
  border-radius: 8px;
  margin: 0 10px;
  max-width: 70%;
  word-wrap: break-word;
}
.my-message .message-content {
  background-color: #a0e75a;
}
.message-content::before {
  content: '';
  position: absolute;
  top: 13px;
  border: 6px solid transparent;
}
.message:not(.my-message) .message-content::before {
  left: -12px;
  border-right-color: #ffffff;
}
.my-message .message-content::before {
  right: -12px;
  border-left-color: #a0e75a;
}
.red-packet-content {
  display: flex;
  align-items: center;
  background-color: #f29e4c;
  color: white;
  border-radius: 8px;
  padding: 10px 15px;
  margin: 0 10px;
  width: 200px;
}
.red-packet-icon {
  font-size: 30px;
  margin-right: 10px;
}
.red-packet-text {
  display: flex;
  flex-direction: column;
}
.red-packet-subtext {
  font-size: 12px;
  opacity: 0.8;
}
.chat-input {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f8f8;
}
.icon {
  width: 30px;
  height: 30px;
  margin: 0 5px;
}
.input-field {
  flex: 1;
  height: 36px;
  padding: 0 12px;
  border-radius: 18px;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
}
.voice-button {
  flex: 1;
  height: 36px;
  line-height: 36px;
  text-align: center;
  border-radius: 18px;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
}
.send-button {
  background-color: #07c160;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 8px 12px;
  margin-left: 10px;
}
.sender-picker {
  /* This is a bit of a hack to keep the UI clean */
  /* A better solution might be a custom component */
  position: absolute;
  left: 12px;
  bottom: 50px; /* Position it above the input bar */
  z-index: 10;
}
.sender-switch {
  padding: 5px 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  background-color: #fff;
}
</style>