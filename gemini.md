# 微信聊天生成器小程序 产品需求文档 (PRD)

- **文档版本:** V1.1
- **更新日期:** 2025年6月28日
- **创建人:** 创想家

---

### 1. 产品概述

本项目旨在开发一款名为“微信聊天生成器”（暂定）的工具类小程序。该产品专注于让用户能够轻松、快速地创建高度仿真的微信单人聊天界面截图。用户可以通过自定义双方的头像、昵称，并模拟发送文本、表情、红包、转账等一系列聊天行为，最终生成一张与真实微信聊天记录在视觉上无差异的图片。

根据我们的沟通，本产品初期的核心定位是**面向大众的社交娱乐工具**，满足用户制作搞笑段子、社交媒体趣味内容的需求。

### 2. 产品目标

* **用户目标:** 为用户提供一个比专业修图软件（如PS）更简单、比市面上同类工具更真实的聊天记录生成方案，降低趣味内容的创作门槛。
* **产品目标:**
    * **近期 (MVP):** 实现核心功能，保证产品的可用性和高度的视觉保真度，吸引第一批种子用户。
    * **远期:** 探索基于此核心工具的更多玩法和商业模式，成为该细分领域的头部产品。

### 3. 用户画像与使用场景

* **核心用户画像 (Persona):**
    * **姓名:** 小乐
    * **身份:** 在校大学生，社交媒体重度用户，活跃于多个微信群。
    * **特征:** 热爱网络热点和“梗文化”，喜欢和朋友分享有趣的内容，有一定的表达欲，但不会使用复杂的专业软件。
* **典型使用场景:**
    * **场景一：** 小乐在网上看到了一个关于“如何高情商地向父母要生活费”的热门段子，她想创建一个自己和“妈妈”的搞笑聊天记录版本发到朋友圈，于是她打开小程序，快速设置了自己和妈妈的头像昵称，自导自演了一段对话并生成了截图。
    * **场景二：** 小乐的朋友过生日，她想制作一个“假装转账520元”的聊天记录截图发到群里逗大家开心，她使用小程序中的转账功能，轻松制作了这张图片。

### 4. 功能需求 (Functional Requirements)

#### 4.1 聊天设置模块
- **4.1.1 角色信息配置:** 用户可以分别设置“我方”与“对方”的以下信息：
    - 微信头像（支持从手机相册上传）
    - 微信昵称（支持文本输入）
- **4.1.2 确认生效:** 设置完成后，点击“开始聊天”或类似按钮，进入聊天编辑器界面。

#### 4.2 聊天编辑器模块
- **4.2.1 界面UI:** 编辑器界面需高度还原标准微信聊天界面的UI，包括顶部的对方昵称、返回按钮，以及底部的输入框、功能按钮等。
- **4.2.2 角色切换:** 用户在发送任何内容前，可以选择本次发送的身份是“我方”还是“对方”。
- **4.2.3 消息发送:** 点击“发送”后，对应角色的头像和内容气泡会出现在聊天记录中。
- **4.2.4 内容生成:** 支持生成以下类型的聊天内容：
    - 文本消息
    - 微信自带的小黄脸Emoji表情

#### 4.3 核心组件功能
- **4.3.1 红包功能:**
    - **创建:** 在编辑器中选择“红包”功能，会弹出一个配置窗口，用户可自定义红包金额和祝福语。
    - **生成:** 创建后，聊天记录中出现对应的红包气泡。
    - **交互:** 点击聊天记录中的红包气泡，会弹出一个高仿真的“开红包”动画或界面。根据预设，最终显示为“红包已被领取”或“红包已退回”的详情页。
- **4.3.2 转账功能:**
    - **创建:** 在编辑器中选择“转账”功能，可配置转账金额和备注。
    - **生成:** 创建后，聊天记录中出现对应的转账气泡。
    - **交互:** 点击聊天记录中的转账气泡，会跳转到高仿真的转账详情页，状态可被预设为“待收款”或“已收款”。

#### 4.4 导出与分享模块
- **4.4.1 生成截图:** 用户可随时点击“生成”按钮，将当前聊天记录区域生成一张高清的长截图。
- **4.4.2 保存与分享:** 生成的图片可直接保存到手机相册，或调用微信的分享功能发送给朋友/朋友圈。

### 5. 非功能性需求

- **5.1 视觉保真度:** **(最高优先级)** 所有UI元素，包括字体、颜色、间距、图标、动画效果，都必须“像素级”还原真实微信客户端的体验。
- **5.2 性能:** 图片生成速度快，交互流畅无卡顿。
- **5.3 易用性:** 操作流程直观，用户无需任何教程即可上手。

### 6. 待定与未来范围

- **V1.0 范围外:** 暂不包含群聊模拟、语音消息、视频通话、拍一拍、引用回复等更复杂的功能。这些可作为未来迭代的方向。

### 7. 技术架构选型

#### 7.1 核心框架
- **前端框架:** Uni-app
- **UI 语言/框架:** Vue 3
- **状态管理:** Pinia
- **开发语言:** JavaScript / TypeScript

#### 7.2 选型理由
1.  **uni-app 框架:**
    - **跨端能力:** 使用 `uni-app` 可以“一次开发，多端发布”。虽然我们初期目标是微信小程序，但未来可以极低成本地将应用发布到支付宝小程序、抖音小程序、H5、乃至App（iOS/Android），为产品发展保留了极大的灵活性。
    - **生态成熟:** 基于 Vue 生态，拥有丰富的插件市场和活跃的开发者社区，能快速找到成熟的解决方案。
    - **性能优越:** 针对小程序环境有深度优化，能保证良好的用户体验。

2.  **Vue 3 & Composition API:**
    - **性能更强:** 相比 Vue 2，Vue 3 在打包体积、渲染速度上都有显著提升，非常适合对加载速度要求高的小程序场景。
    - **逻辑组织更优:** `Composition API` 能更好地组织我们这个小程序中的复杂逻辑。例如，“聊天编辑器”模块的状态管理（如当前发言人、消息列表、组件配置等）会变得非常清晰和易于维护。
    - **更好的 TypeScript 支持:** 为代码的健壮性和可维护性提供了有力保障。

3.  **Pinia 状态管理:**
    - `Pinia` 是 Vue 3 官方推荐的状态管理库，它设计极为轻量、简洁，且完美契合 `Composition API`。用它来管理全局状态（如双方用户信息）和页面状态会非常高效。

#### 7.3 简要架构
本项目在 MVP（最小可行产品）阶段为**纯前端架构**，不涉及后端服务器。所有操作和数据处理均在小程序前端完成。
- **视图层 (View):** 使用 Vue 3 组件构建用户界面，实现“像素级”还原。
- **逻辑层 (Logic):** 使用 Composition API 组织业务逻辑，如角色切换、内容生成等。
- **数据层 (Data):** 使用 Pinia 管理全局的用户配置信息，组件内部状态由自身管理。
- **交互层 (Interaction):** 调用 uni-app 提供的 API 与微信小程序底层能力交互（如图片上传、保存到相册等）。