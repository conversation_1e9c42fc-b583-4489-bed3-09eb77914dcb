import { defineStore } from 'pinia';

interface User {
  avatar: string;
  nickname: string;
}

export const useChatStore = defineStore('chat', {
  state: () => ({
    me: {
      avatar: '',
      nickname: '',
    } as User,
    you: {
      avatar: '',
      nickname: '',
    } as User,
  }),
  actions: {
    setMe(user: User) {
      this.me = user;
    },
    setYou(user: User) {
      this.you = user;
    },
  },
});
