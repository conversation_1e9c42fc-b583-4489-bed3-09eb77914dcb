<template>
  <view class="settings-container">
    <view class="user-setting">
      <text>“我”的信息</text>
      <view class="avatar-uploader" @click="uploadAvatar('me')">
        <image class="avatar" :src="me.avatar || defaultAvatar" />
      </view>
      <input class="nickname-input" v-model="me.nickname" placeholder="输入昵称" />
    </view>
    <view class="user-setting">
      <text>“对方”的信息</text>
      <view class="avatar-uploader" @click="uploadAvatar('you')">
        <image class="avatar" :src="you.avatar || defaultAvatar" />
      </view>
      <input class="nickname-input" v-model="you.nickname" placeholder="输入昵称" />
    </view>
    <button @click="startChat">开始聊天</button>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useChatStore } from '../../stores/chat';

const chatStore = useChatStore();
const me = ref({ ...chatStore.me });
const you = ref({ ...chatStore.you });

const defaultAvatar = '../../static/logo.png'; // Replace with your default avatar

const uploadAvatar = (user: 'me' | 'you') => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      if (user === 'me') {
        me.value.avatar = res.tempFilePaths[0];
      } else {
        you.value.avatar = res.tempFilePaths[0];
      }
    },
  });
};

const startChat = () => {
  chatStore.setMe(me.value);
  chatStore.setYou(you.value);
  uni.navigateTo({ url: '/pages/chat/index' });
};
</script>

<style scoped>
.settings-container {
  padding: 20px;
}
.user-setting {
  margin-bottom: 20px;
}
.avatar-uploader {
  width: 80px;
  height: 80px;
  border: 1px solid #ccc;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 5px;
}
.nickname-input {
  width: 100%;
  border: 1px solid #ccc;
  padding: 5px;
  margin-top: 10px;
}
</style>